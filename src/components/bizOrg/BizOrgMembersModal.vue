<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    :title="modalTitle"
    style="width: 100vw; height: 100vh; max-width: 100vw; max-height: 100vh"
    :mask-closable="false"
    :closable="true"
    @close="handleClose"
  >
    <div class="members-modal-content">
      <!-- 工具栏区域 -->
      <n-space class="toolbar" justify="space-between">
        <n-space align="center">
          <!-- 成员选择器组件 -->
          <member-selector
            v-model="selectedMembers"
            mode="multiple"
            label="添加成员"
            :dept-id="currentOrgId"
            size="small"
            style="width: 140px"
            @update:model-value="handleMembersSelected"
          />

          <!-- 批量操作按钮 -->
          <n-button
            v-if="checkedRowKeys.length > 0"
            type="primary"
            size="small"
            @click="showBatchAuthModal = true"
          >
            批量授权 ({{ checkedRowKeys.length }})
          </n-button>

          <n-button
            v-if="checkedRowKeys.length > 0"
            type="error"
            size="small"
            @click="handleBatchDelete"
          >
            批量删除 ({{ checkedRowKeys.length }})
          </n-button>
        </n-space>

        <n-space align="center">
          <n-input
            v-model:value="searchKeywords"
            placeholder="输入关键字搜索"
            size="small"
            style="width: 280px"
            clearable
            @keydown.enter="handleSearch"
          >
            <template #prefix>
              <n-icon>
                <component :is="SearchOutlineIcon" />
              </n-icon>
            </template>
          </n-input>
        </n-space>
      </n-space>

      <!-- 数据表格容器 -->
      <div class="table-container">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="membersData"
          :loading="loading"
          :pagination="false"
          :row-key="(row) => row.id"
          :checked-row-keys="checkedRowKeys"
          @update:checked-row-keys="handleCheckedRowKeysChange"
          :max-height="600"
          class="data-table"
        />
      </div>
    </div>

    <!-- 批量授权弹窗 -->
    <n-modal
      v-model:show="showBatchAuthModal"
      preset="card"
      title="批量授权"
      style="width: 600px"
      :mask-closable="false"
    >
      <n-form
        ref="batchAuthFormRef"
        :model="batchAuthForm"
        label-placement="top"
        label-width="auto"
      >
        <n-form-item label="业务角色" path="businessRole">
          <n-select
            v-model:value="batchAuthForm.businessRole"
            :options="businessRoleOptions"
            placeholder="请选择业务角色（不选择则不修改）"
            clearable
          />
        </n-form-item>

        <n-form-item label="数据范围" path="dataRange">
          <div style="display: flex; flex-direction: column; gap: 8px">
            <!-- 已选择的机构标签区域 -->
            <div
              v-if="selectedDataRangeOrgs.length > 0"
              style="
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                padding: 8px;
                border: 1px solid #e0e0e6;
                border-radius: 6px;
                background-color: #fafafa;
                max-height: 120px;
                overflow-y: auto;
              "
            >
              <n-tag
                v-for="org in selectedDataRangeOrgs"
                :key="org.id"
                type="info"
                size="small"
                closable
                @close="removeSelectedDataRangeOrg(org.id)"
                style="font-size: 12px"
              >
                {{ org.orgName }}
              </n-tag>
            </div>

            <!-- 提示文本（当没有选择机构时） -->
            <div
              v-else
              style="
                color: #999;
                font-size: 14px;
                padding: 8px;
                border: 1px dashed #e0e0e6;
                border-radius: 6px;
                text-align: center;
                background-color: #fafafa;
              "
            >
              点击下方按钮选择数据范围机构
            </div>

            <!-- 操作按钮区域 -->
            <div style="display: flex; gap: 8px">
              <n-button
                type="primary"
                ghost
                @click="showDataRangeSelector = true"
              >
                选择机构
              </n-button>
              <n-button
                v-if="selectedDataRangeOrgs.length > 0"
                type="error"
                ghost
                @click="clearDataRangeSelection"
              >
                清空
              </n-button>
            </div>
          </div>
        </n-form-item>

        <n-form-item>
          <n-space>
            <n-button
              type="primary"
              @click="handleBatchAuth"
              :loading="batchAuthLoading"
            >
              确认授权
            </n-button>
            <n-button @click="showBatchAuthModal = false"> 取消 </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-modal>

    <!-- 数据范围选择器 -->
    <biz-org-selector
      v-model:visible="showDataRangeSelector"
      title="选择数据范围机构"
      @select="handleOrgSelectorSelect"
      @cancel="handleDataRangeSelectorCancel"
    />
  </n-modal>
</template>

<script setup>
import { useBizOrgMembersModal } from "./BizOrgMembersModal.js";

// 使用组合式函数获取所有状态和方法
// 注意：以下变量和方法都是实际使用的，IDE警告是误报
// 原因：1. 表格列配置中的动态使用 2. 计算属性中的使用 3. 事件处理中的使用
/* eslint-disable @typescript-eslint/no-unused-vars */
const {
  // 图标组件 - 在表格列配置中使用
  SearchOutlineIcon,
  CreateOutlineIcon, // 用于表格编辑按钮
  TrashOutlineIcon, // 用于表格删除按钮
  CopyOutlineIcon, // 用于表格复制功能
  CheckmarkOutlineIcon, // 用于表格保存按钮
  CloseOutlineIcon, // 用于表格取消按钮

  // 状态变量
  visible,
  loading,
  currentOrgId,
  searchKeywords,
  tableRef,
  selectedMembers,
  membersData,
  checkedRowKeys,
  showBatchAuthModal,
  batchAuthLoading,
  batchAuthFormRef,
  batchAuthForm,
  showDataRangeSelector,
  selectedDataRangeOrgs,
  businessRoleOptions,
  modalTitle,

  // 数据
  columns,

  // 方法 - 大部分在表格列配置和事件处理中使用
  copyToClipboard, // 用于表格列配置
  openModal,
  handleClose,
  refreshData, // 用于数据刷新逻辑
  handleSearch,
  handleMembersSelected,
  handleEditRow, // 用于表格列配置
  handleSaveEdit, // 用于表格列配置
  handleCancelEdit, // 用于表格列配置
  handleDeleteMember, // 用于表格列配置
  handleBatchDelete,
  handleCheckedRowKeysChange,
  handleBatchAuth,
  handleOrgSelectorSelect,
  handleDataRangeOrgSelect, // 用于机构选择逻辑
  handleDataRangeSelectorCancel,
  clearDataRangeSelection,
  updateDataRangeDisplay, // 用于数据范围显示逻辑
  openDataRangeSelector, // 用于表格列配置
  handleSingleRowDataRangeSelect, // 用于机构选择逻辑
  removeSelectedOrg, // 用于表格列配置
  clearRowDataRange, // 用于表格列配置
  removeSelectedDataRangeOrg,

  // 组件
  MemberSelector,
  BizOrgSelector,
} = useBizOrgMembersModal();
/* eslint-enable @typescript-eslint/no-unused-vars */

// 暴露方法给父组件调用
defineExpose({
  openModal,
});
</script>

<style scoped lang="scss">
@use "./BizOrgMembersModal.scss";
</style>
